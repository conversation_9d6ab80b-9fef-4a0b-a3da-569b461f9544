Humanoid Robot Project Phase 1
1.<PERSON>hil-3D Design & Structure and coordinating the works
2<PERSON><PERSON>-Hardware related project-Identifying Hardware components required for project and integrate with app.
3.Ziyad-Software and app related project-AI tools used for display, response based analysis, LMS Connectivity, App connectivity and control through Phone and Tab.
Project Start date 27th May 2025
End Date 26th June 2025
Testing-27th June 2025
Trial Run-28th June 2025
Implementation-30th June 2025
Process-
1.3D Design
2.Identification of Hardware Components according to requirements
3.Speech to text Conversion (English, Hindi, Malayalam, Arabic)
4.Purchase of equipments
5.Structure purchase order
6.Using of AI tools for response through mobile app.(ios and android)
7.Testing of equipments
8.Structure making
9.Integrating of hardware equipments
10.Integration of software
FEATURES (Basic requirements)
Voice command for physical movements(Move forward, move backward, move right, move left).
Speech to Text for Textual display of contents and LMS.
Hand lifting for saying “Hi” and shake hand.
Linking ChatGPT, LMS and other A I tools through App.
Controlling the robot through mobile app for motion.

Response

1.Speech to text basis: Person will ask questions through speech and the robot will reply through text / image in display and through audio.

2.Speech to action basis: Person will ask the robot to move through speech and the robot will move accordingly.
PHASE 1
1. Core Components and Hardware
   - Crinnoboard
   - Sensors:
     - Motion Sensors* (e.g., accelerometers, gyroscopes) for movement and balance.
     - Ultrasonic/Infrared Sensors for obstacle detection and navigation.
     - Touch Sensors for interaction and to allow students to create tactile responses.
     - Camera Module (optional) for visual recognition and object tracking.
   - Actuators and Motors:
     - Servo Motors for joint and limb movements to simulate human-like gestures.
     - Stepper Motors for precision movements, especially in tasks requiring accurate positioning.
   - Battery Pack: A rechargeable, safe battery with enough capacity to allow the robot to operate for a few hours in a classroom setting.

2. Basic Structural Framework
   - Lightweight Frame: Use materials like aluminum or plastic for durability and ease of movement.
   - Limbs and Joints: Simple movable arms, legs, and head, with limited degrees of freedom to keep costs low and ensure easier programming.
   - 3D Printed Parts (optional): For custom components or casings, 3D printing provides flexibility and durability while remaining lightweight.
3. Programming and Software
   - Programming Environment: Use beginner-friendly languages like Python or Blockly (visual coding) for easy programming.
   - Libraries for Robotics: Include libraries for motor control, sensor data processing, and basic AI functions. ROS (Robot Operating System) can be used if the project scale allows.
   - AI Modules (optional): For advanced schools, integrate basic AI capabilities like speech recognition (e.g., Google’s Speech API) and object recognition to provide more interactive experiences.
   - Curriculum-Aligned Software: Develop or use existing software that allows students to interact with the robot through programming tasks related to their curriculum.
   – Mobile User Interface (UI): A simple mobile UI which can help students control the robot's actions and behavior easily.

5. Educational Objectives and Curriculum Integration
   - Educational Materials: Provide lesson plans, coding exercises, and robotics challenges aligned with STEM curriculum standards.
   - Teacher Training Materials: Guidelines and tutorials to help educators introduce basic robotics and coding concepts.
Advanced Features:
   - Speech Synthesis: To enable basic responses using speech-to-text technology.
   - Wireless Connectivity: Wi-Fi or Bluetooth for remote control and data transfer.